#include "modelmanager.h"
#include "monitorwindow.h"
#include "datasource.h"
#include "datascreen.h"
#include "videomanager.h"
#include "csvreader.h"
#include "data.h"
#include "config_manager.h"
#include "configmanager_qml.h"
#include "dcs.h"
#include <thread>

#include <QApplication>
#include <QLocale>
#include <QTranslator>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QDir>
#include <QFile>
#include <QCoreApplication>

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

//锅炉列表--全局
std::unordered_map<std::string, Boiler*> boiler_map;

//DCS设备列表--全局
extern std::unordered_map<std::string, DCSDevice*> dcs_map;

//协议文件句柄映射--全局
std::unordered_map<std::string, int> protocol_fd_map;

//全局配置管理器
ConfigManager* g_config_manager = nullptr;

int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

#ifdef _WIN32
    // 设置Windows控制台支持UTF-8输出
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台字体为支持中文的字体
    CONSOLE_FONT_INFOEX cfi;
    cfi.cbSize = sizeof(cfi);
    cfi.nFont = 0;
    cfi.dwFontSize.X = 0;
    cfi.dwFontSize.Y = 16;
    cfi.FontFamily = FF_DONTCARE;
    cfi.FontWeight = FW_NORMAL;
    wcscpy_s(cfi.FaceName, L"Consolas");
    SetCurrentConsoleFontEx(GetStdHandle(STD_OUTPUT_HANDLE), FALSE, &cfi);

    debug_printf("控制台编码设置完成，支持中文显示\n");
#endif

    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages) {
        const QString baseName = "SmartBurning_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName)) {
            a.installTranslator(&translator);
            break;
        }
    }

    // 初始化硬件数据采集系统
    //加载配置文件
    debug_printf("主程序: 开始加载配置文件 config.ini\n");
    debug_printf("主程序: 当前工作目录: %s\n", QDir::currentPath().toStdString().c_str());

    // 检查配置文件是否存在
    QString configPath = "config.ini";
    if (!QFile::exists(configPath)) {
        debug_printf("主程序: 配置文件 %s 不存在，尝试使用应用程序目录\n", configPath.toStdString().c_str());
        configPath = QCoreApplication::applicationDirPath() + "/config.ini";
        debug_printf("主程序: 尝试路径: %s\n", configPath.toStdString().c_str());
    }

    // 确保必要的目录存在
    QString dataDir = "data";
    if (!QDir().exists(dataDir)) {
        if (QDir().mkpath(dataDir)) {
            debug_printf("主程序: 创建data目录成功: %s\n", QDir().absoluteFilePath(dataDir).toStdString().c_str());
        } else {
            debug_printf("主程序: 创建data目录失败: %s\n", dataDir.toStdString().c_str());
        }
    } else {
        debug_printf("主程序: data目录已存在: %s\n", QDir().absoluteFilePath(dataDir).toStdString().c_str());
    }

    ConfigManager config(configPath.toStdString());
    bool config_loaded = config.load();

    // 设置全局配置管理器指针
    g_config_manager = &config;
    if (config_loaded) {
        debug_printf("主程序: 配置文件加载成功\n");

        // 测试读取配置
        std::string boiler_list = config.get<std::string>("BoilerList", "list");
        debug_printf("主程序: 读取到的锅炉列表: '%s'\n", boiler_list.c_str());

        std::string protocol_list = config.get<std::string>("ProtocolList", "list");
        debug_printf("主程序: 读取到的协议列表: '%s'\n", protocol_list.c_str());

    } else {
        debug_printf("主程序: 配置文件加载失败\n");
    }

    //启动硬件驱动文件句柄
    protocol_fd_map = start_fd(&config);

    //创建所有锅炉列表
    debug_printf("主程序: 开始创建锅炉列表\n");
    boiler_map = get_boiler_list(&config);
    debug_printf("主程序: 锅炉列表创建完成，数量: %zu\n", boiler_map.size());

    //创建所有DCS设备列表
    debug_printf("主程序: 开始创建DCS设备列表\n");
    dcs_map = get_dcs_list(&config);
    debug_printf("主程序: DCS设备列表创建完成，数量: %zu\n", dcs_map.size());

    //遍历分别启动采集线程（采用hello项目的多线程模式）
    for (const auto& pair : boiler_map) {
        debug_printf("主程序: 配置锅炉 '%s'\n", pair.first.c_str());
        std::string protocol_name = config.get<std::string>(pair.second->boiler_name, "Protocol");
        debug_printf("主程序: 锅炉 '%s' 使用协议 '%s'\n", pair.first.c_str(), protocol_name.c_str());
        //设置文件句柄
        pair.second->fd = protocol_fd_map[protocol_name];
        debug_printf("主程序: 锅炉 '%s' 文件句柄: %d\n", pair.first.c_str(), pair.second->fd);
        //启动数据采集线程
        pair.second->start_data_collect();
        debug_printf("主程序: 锅炉 '%s' 数据采集线程已启动\n", pair.first.c_str());
    }

    //遍历分别启动DCS设备采集线程
    for (const auto& pair : dcs_map) {
        debug_printf("主程序: 配置DCS设备 '%s'\n", pair.first.c_str());
        std::string protocol_name = config.get<std::string>(pair.second->dcs_name, "Protocol");
        debug_printf("主程序: DCS设备 '%s' 使用协议 '%s'\n", pair.first.c_str(), protocol_name.c_str());
        //设置文件句柄
        pair.second->fd = protocol_fd_map[protocol_name];
        debug_printf("主程序: DCS设备 '%s' 文件句柄: %d\n", pair.first.c_str(), pair.second->fd);
        //启动数据采集线程
        pair.second->start_data_collect();
        debug_printf("主程序: DCS设备 '%s' 数据采集线程已启动\n", pair.first.c_str());
    }

#ifdef DEBUG
    extern bool ready;
    std::thread{[]{    //此处使用了lambda函数
            debug_printf("\n启动获取实时数据获取debug\n");
            while (!ready){
                float co = 0.00,o2 = 0.00;
                float current = 0.00, voltage =0.00, temperature = 0.00;
                float nox = 0.00, so2 = 0.0;

                // 遍历所有配置的锅炉进行调试数据获取
                for (const auto& pair : boiler_map) {
                    const std::string& boiler_name = pair.first;
                    get_realtime_data(boiler_name, &co, &o2, &nox, &so2, &current, &voltage, &temperature);
                    debug_printf("%s 采集到数据 %.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\n",
                           boiler_name.c_str(), co, o2, nox, so2, current, voltage, temperature);
                }

                // 遍历所有配置的DCS设备进行调试数据获取
                for (const auto& pair : dcs_map) {
                    const std::string& dcs_name = pair.first;
                    float furnace_pressure = 0.0f, superheater_temp = 0.0f, generator_power = 0.0f;
                    float main_steam_pressure = 0.0f, total_air_flow = 0.0f, water_coal_ratio = 0.0f;
                    float dcs_co = 0.0f, dcs_o2 = 0.0f;
                    float primary_fan_a = 0.0f, primary_fan_b = 0.0f, fan_a = 0.0f;
                    float fan_b = 0.0f, induced_fan_a = 0.0f, induced_fan_b = 0.0f;

                    get_realtime_dcs_data(dcs_name, &furnace_pressure, &superheater_temp, &generator_power,
                                         &main_steam_pressure, &total_air_flow, &water_coal_ratio, &dcs_co, &dcs_o2,
                                         &primary_fan_a, &primary_fan_b, &fan_a, &fan_b, &induced_fan_a, &induced_fan_b);
                    debug_printf("%s DCS采集到数据 炉膛压力=%.2f, 过热器温度=%.2f, 发电机功率=%.2f, 主蒸汽压力=%.2f, 总风量=%.2f, 水煤比=%.2f, CO=%.2f, O2=%.2f\n",
                           dcs_name.c_str(), furnace_pressure, superheater_temp, generator_power, main_steam_pressure,
                           total_air_flow, water_coal_ratio, dcs_co, dcs_o2);
                    debug_printf("%s DCS风机数据 一次风机A=%.4f, 一次风机B=%.4f, 送风机A=%.4f, 送风机B=%.4f, 引风机A=%.4f, 引风机B=%.4f\n",
                           dcs_name.c_str(), primary_fan_a, primary_fan_b, fan_a, fan_b, induced_fan_a, induced_fan_b);
                }

               std::this_thread::sleep_for(std::chrono::seconds(3));
            }
            debug_printf("\n退出获取实时数据debug\n");
      }
    }.detach();
#endif
    
    // 创建模型管理器实例
    ModelManager modelManager;

    // 创建监控窗口实例
    MonitorWindow monitorWindow;

    // 创建数据大屏实例
    DataScreen dataScreen;

    // 创建视频管理器实例
    VideoManager videoManager;

    // 创建CSV读取器实例
    CsvReader csvReader;

    // 创建配置管理器QML包装实例
    ConfigManagerQML configManagerQML;
    configManagerQML.setConfigManager(&config);

    // 创建QML引擎
    QQmlApplicationEngine engine;

    // 将实例注册到QML上下文
    engine.rootContext()->setContextProperty("modelManager", &modelManager);
    engine.rootContext()->setContextProperty("monitorWindow", &monitorWindow);
    engine.rootContext()->setContextProperty("dataScreen", &dataScreen);
    engine.rootContext()->setContextProperty("videoManager", &videoManager);
    engine.rootContext()->setContextProperty("csvReader", &csvReader);
    engine.rootContext()->setContextProperty("configManagerQML", &configManagerQML);
    
    // 加载主QML文件
    engine.load(QUrl(QStringLiteral("qrc:/main.qml")));
    
    if (engine.rootObjects().isEmpty()) {
        return -1;
    }
    
    return a.exec();
}
