/****************************************************************************
** Meta object code from reading C++ file 'monitoring_datasource.h'
**
** Created by: The Qt Meta Object Compiler version 69 (Qt 6.9.1)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../monitoring_datasource.h"
#include <QtCore/qmetatype.h>

#include <QtCore/qtmochelpers.h>

#include <memory>


#include <QtCore/qxptype_traits.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'monitoring_datasource.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 69
#error "This file was generated using the moc from 6.9.1. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {
struct qt_meta_tag_ZN20MonitoringDataSourceE_t {};
} // unnamed namespace

template <> constexpr inline auto MonitoringDataSource::qt_create_metaobjectdata<qt_meta_tag_ZN20MonitoringDataSourceE_t>()
{
    namespace QMC = QtMocConstants;
    QtMocHelpers::StringRefStorage qt_stringData {
        "MonitoringDataSource",
        "smokeDataChanged",
        "",
        "smokeTableDataChanged",
        "boilerListChanged",
        "currentBoilerChanged",
        "isRunningChanged",
        "chartDataUpdated",
        "dataConnectionChanged",
        "currentDataChanged",
        "startMonitoring",
        "stopMonitoring",
        "clearData",
        "updateSmokeChartSeries",
        "QAbstractSeries*",
        "o2Series",
        "coSeries",
        "noxSeries",
        "so2Series",
        "zoomIndex",
        "updateSmokeChartSeriesWithMinutes",
        "updateSmokeChartSeriesWithScroll",
        "scrollOffset",
        "updateData",
        "smokeO2Data",
        "QVariantList",
        "smokeCOData",
        "smokeNOxData",
        "smokeSO2Data",
        "smokeTableData",
        "boilerList",
        "currentBoiler",
        "isRunning",
        "isDataConnected",
        "connectionStatus",
        "currentTemperature",
        "currentVoltage",
        "currentCurrent"
    };

    QtMocHelpers::UintData qt_methods {
        // Signal 'smokeDataChanged'
        QtMocHelpers::SignalData<void()>(1, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'smokeTableDataChanged'
        QtMocHelpers::SignalData<void()>(3, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'boilerListChanged'
        QtMocHelpers::SignalData<void()>(4, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentBoilerChanged'
        QtMocHelpers::SignalData<void()>(5, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'isRunningChanged'
        QtMocHelpers::SignalData<void()>(6, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'chartDataUpdated'
        QtMocHelpers::SignalData<void()>(7, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'dataConnectionChanged'
        QtMocHelpers::SignalData<void()>(8, 2, QMC::AccessPublic, QMetaType::Void),
        // Signal 'currentDataChanged'
        QtMocHelpers::SignalData<void()>(9, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'startMonitoring'
        QtMocHelpers::SlotData<void()>(10, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'stopMonitoring'
        QtMocHelpers::SlotData<void()>(11, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'clearData'
        QtMocHelpers::SlotData<void()>(12, 2, QMC::AccessPublic, QMetaType::Void),
        // Slot 'updateSmokeChartSeries'
        QtMocHelpers::SlotData<void(QAbstractSeries *, QAbstractSeries *, QAbstractSeries *, QAbstractSeries *, int)>(13, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 14, 15 }, { 0x80000000 | 14, 16 }, { 0x80000000 | 14, 17 }, { 0x80000000 | 14, 18 },
            { QMetaType::Int, 19 },
        }}),
        // Slot 'updateSmokeChartSeries'
        QtMocHelpers::SlotData<void(QAbstractSeries *, QAbstractSeries *, QAbstractSeries *, QAbstractSeries *)>(13, 2, QMC::AccessPublic | QMC::MethodCloned, QMetaType::Void, {{
            { 0x80000000 | 14, 15 }, { 0x80000000 | 14, 16 }, { 0x80000000 | 14, 17 }, { 0x80000000 | 14, 18 },
        }}),
        // Slot 'updateSmokeChartSeriesWithMinutes'
        QtMocHelpers::SlotData<void(QAbstractSeries *, QAbstractSeries *, QAbstractSeries *, QAbstractSeries *)>(20, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 14, 15 }, { 0x80000000 | 14, 16 }, { 0x80000000 | 14, 17 }, { 0x80000000 | 14, 18 },
        }}),
        // Slot 'updateSmokeChartSeriesWithScroll'
        QtMocHelpers::SlotData<void(QAbstractSeries *, QAbstractSeries *, QAbstractSeries *, QAbstractSeries *, int, double)>(21, 2, QMC::AccessPublic, QMetaType::Void, {{
            { 0x80000000 | 14, 15 }, { 0x80000000 | 14, 16 }, { 0x80000000 | 14, 17 }, { 0x80000000 | 14, 18 },
            { QMetaType::Int, 19 }, { QMetaType::Double, 22 },
        }}),
        // Slot 'updateData'
        QtMocHelpers::SlotData<void()>(23, 2, QMC::AccessPrivate, QMetaType::Void),
    };
    QtMocHelpers::UintData qt_properties {
        // property 'smokeO2Data'
        QtMocHelpers::PropertyData<QVariantList>(24, 0x80000000 | 25, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 0),
        // property 'smokeCOData'
        QtMocHelpers::PropertyData<QVariantList>(26, 0x80000000 | 25, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 0),
        // property 'smokeNOxData'
        QtMocHelpers::PropertyData<QVariantList>(27, 0x80000000 | 25, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 0),
        // property 'smokeSO2Data'
        QtMocHelpers::PropertyData<QVariantList>(28, 0x80000000 | 25, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 0),
        // property 'smokeTableData'
        QtMocHelpers::PropertyData<QVariantList>(29, 0x80000000 | 25, QMC::DefaultPropertyFlags | QMC::EnumOrFlag, 1),
        // property 'boilerList'
        QtMocHelpers::PropertyData<QStringList>(30, QMetaType::QStringList, QMC::DefaultPropertyFlags, 2),
        // property 'currentBoiler'
        QtMocHelpers::PropertyData<QString>(31, QMetaType::QString, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 3),
        // property 'isRunning'
        QtMocHelpers::PropertyData<bool>(32, QMetaType::Bool, QMC::DefaultPropertyFlags | QMC::Writable | QMC::StdCppSet, 4),
        // property 'isDataConnected'
        QtMocHelpers::PropertyData<bool>(33, QMetaType::Bool, QMC::DefaultPropertyFlags, 6),
        // property 'connectionStatus'
        QtMocHelpers::PropertyData<QString>(34, QMetaType::QString, QMC::DefaultPropertyFlags, 6),
        // property 'currentTemperature'
        QtMocHelpers::PropertyData<QString>(35, QMetaType::QString, QMC::DefaultPropertyFlags, 7),
        // property 'currentVoltage'
        QtMocHelpers::PropertyData<QString>(36, QMetaType::QString, QMC::DefaultPropertyFlags, 7),
        // property 'currentCurrent'
        QtMocHelpers::PropertyData<QString>(37, QMetaType::QString, QMC::DefaultPropertyFlags, 7),
    };
    QtMocHelpers::UintData qt_enums {
    };
    return QtMocHelpers::metaObjectData<MonitoringDataSource, qt_meta_tag_ZN20MonitoringDataSourceE_t>(QMC::MetaObjectFlag{}, qt_stringData,
            qt_methods, qt_properties, qt_enums);
}
Q_CONSTINIT const QMetaObject MonitoringDataSource::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20MonitoringDataSourceE_t>.stringdata,
    qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20MonitoringDataSourceE_t>.data,
    qt_static_metacall,
    nullptr,
    qt_staticMetaObjectRelocatingContent<qt_meta_tag_ZN20MonitoringDataSourceE_t>.metaTypes,
    nullptr
} };

void MonitoringDataSource::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    auto *_t = static_cast<MonitoringDataSource *>(_o);
    if (_c == QMetaObject::InvokeMetaMethod) {
        switch (_id) {
        case 0: _t->smokeDataChanged(); break;
        case 1: _t->smokeTableDataChanged(); break;
        case 2: _t->boilerListChanged(); break;
        case 3: _t->currentBoilerChanged(); break;
        case 4: _t->isRunningChanged(); break;
        case 5: _t->chartDataUpdated(); break;
        case 6: _t->dataConnectionChanged(); break;
        case 7: _t->currentDataChanged(); break;
        case 8: _t->startMonitoring(); break;
        case 9: _t->stopMonitoring(); break;
        case 10: _t->clearData(); break;
        case 11: _t->updateSmokeChartSeries((*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[5]))); break;
        case 12: _t->updateSmokeChartSeries((*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[4]))); break;
        case 13: _t->updateSmokeChartSeriesWithMinutes((*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[4]))); break;
        case 14: _t->updateSmokeChartSeriesWithScroll((*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<QAbstractSeries*>>(_a[4])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[5])),(*reinterpret_cast< std::add_pointer_t<double>>(_a[6]))); break;
        case 15: _t->updateData(); break;
        default: ;
        }
    }
    if (_c == QMetaObject::IndexOfMethod) {
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::smokeDataChanged, 0))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::smokeTableDataChanged, 1))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::boilerListChanged, 2))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::currentBoilerChanged, 3))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::isRunningChanged, 4))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::chartDataUpdated, 5))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::dataConnectionChanged, 6))
            return;
        if (QtMocHelpers::indexOfMethod<void (MonitoringDataSource::*)()>(_a, &MonitoringDataSource::currentDataChanged, 7))
            return;
    }
    if (_c == QMetaObject::ReadProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 0: *reinterpret_cast<QVariantList*>(_v) = _t->smokeO2Data(); break;
        case 1: *reinterpret_cast<QVariantList*>(_v) = _t->smokeCOData(); break;
        case 2: *reinterpret_cast<QVariantList*>(_v) = _t->smokeNOxData(); break;
        case 3: *reinterpret_cast<QVariantList*>(_v) = _t->smokeSO2Data(); break;
        case 4: *reinterpret_cast<QVariantList*>(_v) = _t->smokeTableData(); break;
        case 5: *reinterpret_cast<QStringList*>(_v) = _t->boilerList(); break;
        case 6: *reinterpret_cast<QString*>(_v) = _t->currentBoiler(); break;
        case 7: *reinterpret_cast<bool*>(_v) = _t->isRunning(); break;
        case 8: *reinterpret_cast<bool*>(_v) = _t->isDataConnected(); break;
        case 9: *reinterpret_cast<QString*>(_v) = _t->connectionStatus(); break;
        case 10: *reinterpret_cast<QString*>(_v) = _t->currentTemperature(); break;
        case 11: *reinterpret_cast<QString*>(_v) = _t->currentVoltage(); break;
        case 12: *reinterpret_cast<QString*>(_v) = _t->currentCurrent(); break;
        default: break;
        }
    }
    if (_c == QMetaObject::WriteProperty) {
        void *_v = _a[0];
        switch (_id) {
        case 6: _t->setCurrentBoiler(*reinterpret_cast<QString*>(_v)); break;
        case 7: _t->setIsRunning(*reinterpret_cast<bool*>(_v)); break;
        default: break;
        }
    }
}

const QMetaObject *MonitoringDataSource::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MonitoringDataSource::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_staticMetaObjectStaticContent<qt_meta_tag_ZN20MonitoringDataSourceE_t>.strings))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int MonitoringDataSource::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    }
    if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 16;
    }
    if (_c == QMetaObject::ReadProperty || _c == QMetaObject::WriteProperty
            || _c == QMetaObject::ResetProperty || _c == QMetaObject::BindableProperty
            || _c == QMetaObject::RegisterPropertyMetaType) {
        qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void MonitoringDataSource::smokeDataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 0, nullptr);
}

// SIGNAL 1
void MonitoringDataSource::smokeTableDataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void MonitoringDataSource::boilerListChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void MonitoringDataSource::currentBoilerChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void MonitoringDataSource::isRunningChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void MonitoringDataSource::chartDataUpdated()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void MonitoringDataSource::dataConnectionChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 6, nullptr);
}

// SIGNAL 7
void MonitoringDataSource::currentDataChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 7, nullptr);
}
QT_WARNING_POP
